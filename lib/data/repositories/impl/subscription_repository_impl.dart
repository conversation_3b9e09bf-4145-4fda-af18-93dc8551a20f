import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/utils/utils.dart';
import 'package:http/http.dart' as http;
import 'package:in_app_purchase/in_app_purchase.dart';

import 'package:supabase_flutter/supabase_flutter.dart';

class SubscriptionRepositoryImpl implements SubscriptionRepository {
  final InAppPurchase _inAppPurchase;
  final SupabaseClient _supabaseClient;
  final _purchaseResultStreamController =
      StreamController<PurchaseResult>.broadcast();

  StreamSubscription<List<PurchaseDetails>>? _purchaseStreamSubscription;
  bool isRestore = false;
  bool _isDisposed = false;

  // iOS delay mechanism for handling multiple purchase events
  Timer? _delayTimer;
  final List<SubscriptionEntity> _accumulatedPurchases = [];

  SubscriptionRepositoryImpl(this._inAppPurchase, this._supabaseClient) {
    _purchaseStreamSubscription =
        _inAppPurchase.purchaseStream.listen(_handlePurchaseUpdates);
  }

  @override
  void dispose() {
    if (_isDisposed) return;
    _isDisposed = true;
    _delayTimer?.cancel();
    _purchaseStreamSubscription?.cancel();
    _purchaseResultStreamController.close();
  }

  @override
  Stream<PurchaseResult> get purchaseResultStream =>
      _purchaseResultStreamController.stream;

  void _handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) {
    if (_isDisposed) return;

    // Debug logging for iOS to understand what's happening
    if (Platform.isIOS) {
      print('=== iOS Purchase Update Debug ===');
      print('isRestore: $isRestore');
      print('Purchase count: ${purchaseDetailsList.length}');
      for (int i = 0; i < purchaseDetailsList.length; i++) {
        final purchase = purchaseDetailsList[i];
        print('Purchase $i:');
        print('  - Product ID: ${purchase.productID}');
        print('  - Status: ${purchase.status}');
        print('  - Transaction Date: ${purchase.transactionDate}');
        print('  - Purchase ID: ${purchase.purchaseID}');
      }
      print('================================');
    }

    // Handle error and cancelled states immediately for non-restore operations
    if (!isRestore) {
      final lastPurchase = purchaseDetailsList.last;
      if (lastPurchase.status == PurchaseStatus.error) {
        _cancelDelayAndClearAccumulated();
        _purchaseResultStreamController.add(
          PurchaseResult.error(message: lastPurchase.error!.message),
        );
        return;
      } else if (lastPurchase.status == PurchaseStatus.canceled) {
        _cancelDelayAndClearAccumulated();
        _purchaseResultStreamController.add(const PurchaseResult.cancelled());
        return;
      }
    }

    // Process purchases and add to accumulated list
    for (final purchaseDetails in purchaseDetailsList) {
      String purchaseToken;

      if (Platform.isIOS) {
        purchaseToken = purchaseDetails.verificationData.localVerificationData;
      } else {
        purchaseToken = purchaseDetails.verificationData.serverVerificationData;
      }

      final subscriptionEntity = SubscriptionEntity(
        productId: purchaseDetails.productID,
        platform: getPlatform(),
        purchaseToken: purchaseToken,
        purchaseId: purchaseDetails.purchaseID,
      );

      // Add to accumulated purchases if not already present (avoid duplicates)
      if (!_accumulatedPurchases
          .any((p) => p.purchaseId == subscriptionEntity.purchaseId)) {
        _accumulatedPurchases.add(subscriptionEntity);
      }

      if (purchaseDetails.pendingCompletePurchase) {
        _inAppPurchase.completePurchase(purchaseDetails);
      }
    }

    // For Android, emit immediately as it provides all purchases in one event
    if (Platform.isAndroid) {
      _emitSuccessResult();
      return;
    }

    // For iOS, use delay mechanism to accumulate multiple events
    _cancelDelayAndStartNew();
  }

  void _cancelDelayAndClearAccumulated() {
    _delayTimer?.cancel();
    _delayTimer = null;
    _accumulatedPurchases.clear();
  }

  void _cancelDelayAndStartNew() {
    _delayTimer?.cancel();
    _delayTimer = Timer(const Duration(milliseconds: 1500), () {
      if (!_isDisposed) {
        _emitSuccessResult();
      }
    });
  }

  void _emitSuccessResult() {
    if (_accumulatedPurchases.isNotEmpty) {
      final purchasesToEmit =
          List<SubscriptionEntity>.from(_accumulatedPurchases);
      _accumulatedPurchases.clear();
      _delayTimer = null;

      _purchaseResultStreamController.add(
        PurchaseResult.success(purchases: purchasesToEmit),
      );
    }
  }

  @override
  Future<ProductDetails> getProductDetail(String productId) async {
    try {
      final ProductDetailsResponse response =
          await _inAppPurchase.queryProductDetails({productId});

      if (response.error != null || response.productDetails.isEmpty) {
        throw ExceptionHandler.handleError(response.error!.message);
      }

      return response.productDetails.first;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> purchaseProduct(PurchaseParam purchaseParam) async {
    isRestore = false;

    try {
      final bool success =
          await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);

      if (!success) {
        throw ExceptionHandler.handleError(
            Str.current.failedToInitiatePurchase);
      }
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> restorePurchases(String userId) async {
    isRestore = true;
    try {
      await _inAppPurchase.restorePurchases(applicationUserName: userId);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<bool> isInAppPurchaseAvailable() async {
    return await _inAppPurchase.isAvailable();
  }

  @override
  Future<SubscriptionVerificationResponse?> verifyAndSubmit(
      SubscriptionVerificationRequest request) async {
    try {
      final response = await http.post(
        Uri.parse(verifyAndSubmitPurchaseFunctionUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization':
              _supabaseClient.auth.currentSession!.accessToken.bearer,
        },
        body: jsonEncode(request.toJson()),
      );

      return SubscriptionVerificationResponse.fromJson(
          jsonDecode(response.body));
    } catch (_) {
      return null;
    }
  }
}
